
# 双冥想小程序后端API测试文件
# 服务器地址: http://localhost:3000
# 注意：需要先执行登录接口获取token，然后在需要认证的接口中使用

### 变量定义
@baseUrl = http://localhost:3000
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjQiLCJvcGVuaWQiOiJvcGVuaWRfdGVzdF8xMjMiLCJpYXQiOjE3NTQ4OTkwMDksImV4cCI6MTc1NzQ5MTAwOX0.w0pvzpB_BM4gJHjj9msNeMUWXXD7btYTzSjyTlDsxmQ

### ========== 公共接口（无需认证） ==========

### 1. 测试公共GET接口
GET {{baseUrl}}/public/get

### 2. 公共API测试 - GET
GET {{baseUrl}}/public/api/test?param1=value1&param2=value2

### 3. 微信用户登录
POST {{baseUrl}}/api/public/user/login
Content-Type: application/json

{
  "openid": "openid_test_123",
  "nickname": "测试用户",
  "avatar_url": "https://youke1.picui.cn/s1/2025/08/11/68999a8cbe3d0.png",
  "unionid": "unionid_test_123"
}

### ========== 通用API接口 ==========

### 4. API测试 - POST
POST {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test",
  "value": "测试数据"
}

### 5. API测试 - PUT
PUT {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test",
  "value": "更新数据"
}

### 6. API测试 - DELETE
DELETE {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test"
}

### ========== 认证相关接口 ==========

### 7. 检查认证
POST {{baseUrl}}/auth/check
Content-Type: application/json
Authorization: Bearer {{token}}

### ========== 用户相关接口 ==========

### 8. 获取用户基础信息
GET {{baseUrl}}/api/user/profile
Authorization: Bearer {{token}}

### 9. 获取用户收藏列表
GET {{baseUrl}}/api/user/favorites?pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### 10. 获取用户多肉列表
GET {{baseUrl}}/api/user/plants
Authorization: Bearer {{token}}

### 11. 获取用户冥想统计
GET {{baseUrl}}/api/user/meditation-stats
Authorization: Bearer {{token}}

### ========== 冥想内容相关接口 ==========

### 12. 获取冥想内容列表
GET {{baseUrl}}/api/meditation/list?pageNum=1&pageSize=10&type=meditation
Authorization: Bearer {{token}}

### 13. 获取冥想内容详情
GET {{baseUrl}}/api/meditation/1
Authorization: Bearer {{token}}

### 14. 收藏/取消收藏冥想内容
POST {{baseUrl}}/api/meditation/1/favorite
Content-Type: application/json
Authorization: Bearer {{token}}

### 15. 搜索冥想内容
GET {{baseUrl}}/api/meditation/search?keyword=冥想&type=冥想&pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### 16. 获取冥想标签列表
GET {{baseUrl}}/api/meditation/tags
Authorization: Bearer {{token}}

### ========== 计划相关接口 ==========

### 17. 获取每日计划
GET {{baseUrl}}/api/plan/daily?date=2025-08-11
Authorization: Bearer {{token}}

### 18. 添加到计划
POST {{baseUrl}}/api/plan/add
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "meditation_id": 1,
  "plan_date": "2025-08-11"
}

### 19. 从计划删除
DELETE {{baseUrl}}/api/plan/item/1
Authorization: Bearer {{token}}

### 20. 完成计划项
PUT {{baseUrl}}/api/plan/item/1/complete
Content-Type: application/json
Authorization: Bearer {{token}}

### 21. 获取历史计划
GET {{baseUrl}}/api/plan/history?pageNum=1&pageSize=10&start_date=2025-08-01&end_date=2025-08-11
Authorization: Bearer {{token}}

### 22. 获取计划统计
GET {{baseUrl}}/api/plan/stats?start_date=2025-08-01&end_date=2025-08-11
Authorization: Bearer {{token}}

### ========== 多肉相关接口 ==========

### 23. 获取多肉列表
GET {{baseUrl}}/api/plant/list
Authorization: Bearer {{token}}

### 24. 获取多肉详情
GET {{baseUrl}}/api/plant/1
Authorization: Bearer {{token}}

### 25. 为多肉增加能量
POST {{baseUrl}}/api/plant/1/energy
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "energy_value": 10,
  "reason": "完成冥想"
}

### 26. 创建新多肉
POST {{baseUrl}}/api/plant/create
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "species": "仙人掌"
}

### 27. 获取多肉成长记录
GET {{baseUrl}}/api/plant/1/records?pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### ========== 文件上传接口 ==========

### 28. 文件上传 (需要实际文件)
POST {{baseUrl}}/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.jpg"
Content-Type: image/jpeg

< ./test.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 29. 文件上传预检请求
OPTIONS {{baseUrl}}/upload

### ========== Swagger文档接口 ==========

### 30. 访问Swagger UI
GET {{baseUrl}}/swagger

### 31. 获取Swagger JSON
GET {{baseUrl}}/swagger.json

### ========== 测试用例说明 ==========

# 使用说明：
# 1. 首先执行第3个接口（微信用户登录）获取token
# 2. 将返回的token复制到文件顶部的@token变量中
# 3. 执行其他需要认证的接口时会自动使用该token
# 4. 如果token过期，需要重新登录获取新token

# 测试数据说明：
# - openid: 微信用户唯一标识，测试时可使用任意字符串
# - meditation_id: 冥想内容ID，需要先在数据库中存在对应记录
# - plan_date: 计划日期，格式为YYYY-MM-DD
# - plant_id: 多肉ID，通过获取多肉列表接口可以看到具体ID

# 分页参数说明：
# - pageNum: 当前页码，从1开始（支持向后兼容的page参数）
# - pageSize: 每页数量，默认10（支持向后兼容的limit参数）
# - 返回格式包含：total（总数量）、pageNum（当前页码）、pageSize（每页数量）、pages（总页数）、items（数据列表）

# 常见HTTP状态码：
# - 200: 请求成功
# - 400: 请求参数错误
# - 401: 未授权（token无效或过期）
# - 404: 资源不存在
# - 500: 服务器内部错误