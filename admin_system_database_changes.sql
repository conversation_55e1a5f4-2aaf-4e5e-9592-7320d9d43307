-- ========================================
-- 后台管理系统数据库修改脚本
-- 创建时间: 2025-08-18
-- 说明: 为冥想应用添加后台管理系统所需的数据表和字段
-- ========================================

USE meditation_app;
-- ========================================
-- 5. 修改现有表结构
-- ========================================

-- 为冥想内容表添加状态字段和音视频字段（字段已存在，跳过）
-- ALTER TABLE meditation_content
-- ADD COLUMN status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态：草稿/已发布/已归档' AFTER favorite_count,
-- ADD COLUMN audio_url VARCHAR(512) DEFAULT NULL COMMENT '音频文件URL' AFTER cover_url,
-- ADD COLUMN video_url VARCHAR(512) DEFAULT NULL COMMENT '视频文件URL' AFTER audio_url;

-- 为多肉表添加品种关联（字段已存在，跳过）
-- ALTER TABLE plants
-- ADD COLUMN species_id BIGINT DEFAULT NULL COMMENT '品种ID' AFTER species,
-- ADD FOREIGN KEY (species_id) REFERENCES plant_species(id) ON DELETE SET NULL;

-- 更新现有多肉数据的品种关联
-- 临时禁用安全更新模式
SET SQL_SAFE_UPDATES = 0;
UPDATE plants SET species_id = 1 WHERE species = 'succulent';
-- 重新启用安全更新模式
SET SQL_SAFE_UPDATES = 1;

-- ========================================
-- 6. 创建索引优化查询性能
-- ========================================

-- 管理员表索引（如果不存在则创建）
CREATE INDEX IF NOT EXISTS idx_admins_username ON admins(username);
CREATE INDEX IF NOT EXISTS idx_admins_status ON admins(status);
CREATE INDEX IF NOT EXISTS idx_admins_role ON admins(role);

-- 用户等级配置表索引
CREATE INDEX IF NOT EXISTS idx_user_level_configs_level ON user_level_configs(level);

-- 多肉品种配置表索引
CREATE INDEX IF NOT EXISTS idx_plant_species_name ON plant_species(name);
CREATE INDEX IF NOT EXISTS idx_plant_species_rarity ON plant_species(rarity);
CREATE INDEX IF NOT EXISTS idx_plant_species_is_active ON plant_species(is_active);

-- 能量奖励规则表索引
CREATE INDEX IF NOT EXISTS idx_energy_reward_rules_type ON energy_reward_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_energy_reward_rules_active ON energy_reward_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_energy_reward_rules_priority ON energy_reward_rules(priority);

-- 冥想内容表新增索引
CREATE INDEX IF NOT EXISTS idx_meditation_content_status ON meditation_content(status);

-- 多肉表新增索引
CREATE INDEX IF NOT EXISTS idx_plants_species_id ON plants(species_id);

-- ========================================
-- 完成数据库修改
-- ========================================
