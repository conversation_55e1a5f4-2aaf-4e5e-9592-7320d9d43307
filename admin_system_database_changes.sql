-- ========================================
-- 后台管理系统数据库修改脚本
-- 创建时间: 2025-08-18
-- 说明: 为冥想应用添加后台管理系统所需的数据表和字段
-- ========================================

USE meditation_app;

-- ========================================
-- 1. 管理员表
-- ========================================
DROP TABLE IF EXISTS admins;
CREATE TABLE admins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    real_name VARCHAR(64) DEFAULT NULL COMMENT '真实姓名',
    email VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    role ENUM('super_admin', 'admin', 'editor') DEFAULT 'editor' COMMENT '角色：超级管理员/管理员/编辑员',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态：激活/禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 管理员表触发器
DELIMITER $$
CREATE TRIGGER trg_admins_updated_at
BEFORE UPDATE ON admins
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 插入默认超级管理员（密码：admin123）
INSERT INTO admins (username, password, real_name, role, status) VALUES
('admin', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqQqQqQqQqQqQqQ', '超级管理员', 'super_admin', 'active');

-- ========================================
-- 2. 用户等级配置表
-- ========================================
DROP TABLE IF EXISTS user_level_configs;
CREATE TABLE user_level_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level INT NOT NULL UNIQUE COMMENT '等级',
    level_name VARCHAR(64) NOT NULL COMMENT '等级名称',
    required_days INT DEFAULT 0 COMMENT '升级所需连续天数',
    required_duration INT DEFAULT 0 COMMENT '升级所需总冥想时长（秒）',
    benefits TEXT DEFAULT NULL COMMENT '等级权益描述（JSON格式）',
    icon_url VARCHAR(512) DEFAULT NULL COMMENT '等级图标URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级配置表';

-- 用户等级配置表触发器
DELIMITER $$
CREATE TRIGGER trg_user_level_configs_updated_at
BEFORE UPDATE ON user_level_configs
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 插入默认等级配置
INSERT INTO user_level_configs (level, level_name, required_days, required_duration, benefits) VALUES
(1, '初心者', 0, 0, '{"description": "开始冥想之旅", "features": ["基础冥想内容"]}'),
(2, '入门者', 3, 1800, '{"description": "坚持冥想3天", "features": ["解锁更多冥想内容", "获得专属徽章"]}'),
(3, '修行者', 7, 7200, '{"description": "坚持冥想7天", "features": ["解锁高级冥想课程", "能量获取+10%"]}'),
(4, '禅修者', 15, 18000, '{"description": "坚持冥想15天", "features": ["解锁专属多肉品种", "能量获取+20%"]}'),
(5, '大师', 30, 54000, '{"description": "坚持冥想30天", "features": ["解锁所有内容", "能量获取+50%", "专属称号"]}');

-- ========================================
-- 3. 多肉品种配置表
-- ========================================
DROP TABLE IF EXISTS plant_species;
CREATE TABLE plant_species (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(64) NOT NULL UNIQUE COMMENT '品种名称',
    display_name VARCHAR(64) NOT NULL COMMENT '显示名称',
    description TEXT DEFAULT NULL COMMENT '品种描述',
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度：普通/稀有/史诗/传说',
    unlock_condition TEXT DEFAULT NULL COMMENT '解锁条件（JSON格式）',
    growth_stages TEXT DEFAULT NULL COMMENT '成长阶段配置（JSON格式）',
    max_level INT DEFAULT 10 COMMENT '最大等级',
    base_energy_per_level INT DEFAULT 100 COMMENT '每级基础能量需求',
    image_urls TEXT DEFAULT NULL COMMENT '各阶段图片URL（JSON格式）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多肉品种配置表';

-- 多肉品种配置表触发器
DELIMITER $$
CREATE TRIGGER trg_plant_species_updated_at
BEFORE UPDATE ON plant_species
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 插入默认多肉品种
INSERT INTO plant_species (name, display_name, description, rarity, unlock_condition, growth_stages, max_level, base_energy_per_level, image_urls) VALUES
('succulent_basic', '基础多肉', '最常见的多肉植物，适合新手养护', 'common', '{"level": 1}', '{"stages": [{"level": 1, "name": "幼苗"}, {"level": 5, "name": "成长期"}, {"level": 10, "name": "成熟期"}]}', 10, 50, '{"1": "/images/plants/basic_1.png", "5": "/images/plants/basic_5.png", "10": "/images/plants/basic_10.png"}'),
('succulent_rare', '稀有多肉', '较为稀有的多肉品种，需要更多关爱', 'rare', '{"level": 3, "days": 7}', '{"stages": [{"level": 1, "name": "珍贵幼苗"}, {"level": 8, "name": "稀有成长期"}, {"level": 15, "name": "稀有成熟期"}]}', 15, 80, '{"1": "/images/plants/rare_1.png", "8": "/images/plants/rare_8.png", "15": "/images/plants/rare_15.png"}'),
('succulent_epic', '史诗多肉', '极其珍贵的多肉品种，拥有独特外观', 'epic', '{"level": 5, "days": 21}', '{"stages": [{"level": 1, "name": "传说幼苗"}, {"level": 10, "name": "史诗成长期"}, {"level": 20, "name": "史诗成熟期"}]}', 20, 120, '{"1": "/images/plants/epic_1.png", "10": "/images/plants/epic_10.png", "20": "/images/plants/epic_20.png"}');

-- ========================================
-- 4. 能量奖励规则表
-- ========================================
DROP TABLE IF EXISTS energy_reward_rules;
CREATE TABLE energy_reward_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(128) NOT NULL COMMENT '规则名称',
    rule_type ENUM('meditation_complete', 'daily_streak', 'level_up', 'special_event') NOT NULL COMMENT '规则类型：完成冥想/连续天数/升级/特殊事件',
    condition TEXT DEFAULT NULL COMMENT '触发条件（JSON格式）',
    energy_amount INT NOT NULL COMMENT '奖励能量值',
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.00 COMMENT '奖励倍数',
    max_daily_times INT DEFAULT 0 COMMENT '每日最大触发次数（0为无限制）',
    description TEXT DEFAULT NULL COMMENT '规则描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='能量奖励规则表';

-- 能量奖励规则表触发器
DELIMITER $$
CREATE TRIGGER trg_energy_reward_rules_updated_at
BEFORE UPDATE ON energy_reward_rules
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 插入默认奖励规则
INSERT INTO energy_reward_rules (rule_name, rule_type, condition, energy_amount, bonus_multiplier, max_daily_times, description, priority) VALUES
('完成冥想基础奖励', 'meditation_complete', '{"min_duration": 300}', 10, 1.00, 0, '完成5分钟以上冥想获得基础能量奖励', 1),
('完成冥想时长奖励', 'meditation_complete', '{"min_duration": 1800}', 30, 1.00, 3, '完成30分钟冥想获得额外奖励，每日最多3次', 2),
('连续冥想3天', 'daily_streak', '{"streak_days": 3}', 50, 1.00, 1, '连续冥想3天获得奖励', 3),
('连续冥想7天', 'daily_streak', '{"streak_days": 7}', 100, 1.00, 1, '连续冥想7天获得大量奖励', 4),
('等级提升奖励', 'level_up', '{}', 200, 1.00, 0, '用户等级提升时获得奖励', 5);

-- ========================================
-- 5. 修改现有表结构
-- ========================================

-- 为冥想内容表添加状态字段和音视频字段
ALTER TABLE meditation_content 
ADD COLUMN status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态：草稿/已发布/已归档' AFTER favorite_count,
ADD COLUMN audio_url VARCHAR(512) DEFAULT NULL COMMENT '音频文件URL' AFTER cover_url,
ADD COLUMN video_url VARCHAR(512) DEFAULT NULL COMMENT '视频文件URL' AFTER audio_url;

-- 为多肉表添加品种关联
ALTER TABLE plants 
ADD COLUMN species_id BIGINT DEFAULT NULL COMMENT '品种ID' AFTER species,
ADD FOREIGN KEY (species_id) REFERENCES plant_species(id) ON DELETE SET NULL;

-- 更新现有多肉数据的品种关联
UPDATE plants SET species_id = 1 WHERE species = 'succulent';

-- ========================================
-- 6. 创建索引优化查询性能
-- ========================================

-- 管理员表索引
CREATE INDEX idx_admins_username ON admins(username);
CREATE INDEX idx_admins_status ON admins(status);
CREATE INDEX idx_admins_role ON admins(role);

-- 用户等级配置表索引
CREATE INDEX idx_user_level_configs_level ON user_level_configs(level);

-- 多肉品种配置表索引
CREATE INDEX idx_plant_species_name ON plant_species(name);
CREATE INDEX idx_plant_species_rarity ON plant_species(rarity);
CREATE INDEX idx_plant_species_is_active ON plant_species(is_active);

-- 能量奖励规则表索引
CREATE INDEX idx_energy_reward_rules_type ON energy_reward_rules(rule_type);
CREATE INDEX idx_energy_reward_rules_active ON energy_reward_rules(is_active);
CREATE INDEX idx_energy_reward_rules_priority ON energy_reward_rules(priority);

-- 冥想内容表新增索引
CREATE INDEX idx_meditation_content_status ON meditation_content(status);

-- 多肉表新增索引
CREATE INDEX idx_plants_species_id ON plants(species_id);

-- ========================================
-- 完成数据库修改
-- ========================================
